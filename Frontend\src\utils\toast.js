import { toast } from 'react-toastify';

// Toast configuration
const toastConfig = {
  position: "top-right",
  autoClose: 5000,
  hideProgressBar: false,
  closeOnClick: true,
  pauseOnHover: true,
  draggable: true,
  progress: undefined,
  theme: "light",
};

// Success toast
export const showSuccess = (message, options = {}) => {
  toast.success(message, {
    ...toastConfig,
    ...options,
  });
};

// Error toast
export const showError = (message, options = {}) => {
  toast.error(message, {
    ...toastConfig,
    ...options,
  });
};

// Warning toast
export const showWarning = (message, options = {}) => {
  toast.warning(message, {
    ...toastConfig,
    ...options,
  });
};

// Info toast
export const showInfo = (message, options = {}) => {
  toast.info(message, {
    ...toastConfig,
    ...options,
  });
};

// Loading toast
export const showLoading = (message = "Loading...", options = {}) => {
  return toast.loading(message, {
    ...toastConfig,
    ...options,
  });
};

// Update existing toast
export const updateToast = (toastId, message, type = 'success', options = {}) => {
  toast.update(toastId, {
    render: message,
    type: type,
    isLoading: false,
    ...toastConfig,
    ...options,
  });
};

// Dismiss toast
export const dismissToast = (toastId) => {
  toast.dismiss(toastId);
};

// Dismiss all toasts
export const dismissAllToasts = () => {
  toast.dismiss();
};

// Custom toast for OTP scenarios
export const showOTPSuccess = (message = "OTP sent successfully!") => {
  showSuccess(message, {
    autoClose: 3000,
  });
};

export const showOTPError = (message = "Failed to send OTP. Please try again.") => {
  showError(message, {
    autoClose: 4000,
  });
};

export const showOTPCooldown = (seconds) => {
  showWarning(`Please wait ${seconds} seconds before requesting another OTP`, {
    autoClose: seconds * 1000,
  });
};

export const showOTPVerificationSuccess = (message = "OTP verified successfully!") => {
  showSuccess(message, {
    autoClose: 2000,
  });
};

export const showOTPVerificationError = (message = "Invalid OTP. Please try again.") => {
  showError(message, {
    autoClose: 4000,
  });
};

// Authentication related toasts
export const showLoginSuccess = (userName = "User") => {
  showSuccess(`Welcome back, ${userName}!`, {
    autoClose: 3000,
  });
};

export const showRegistrationSuccess = () => {
  showSuccess("Registration successful! Please verify your OTP.", {
    autoClose: 4000,
  });
};

export const showLogoutSuccess = () => {
  showInfo("You have been logged out successfully.", {
    autoClose: 2000,
  });
};

// Network error toast
export const showNetworkError = () => {
  showError("Network error. Please check your connection and try again.", {
    autoClose: 6000,
  });
};

// Generic API error handler
export const showAPIError = (error) => {
  let message = "An unexpected error occurred. Please try again.";
  
  if (error?.response?.data?.message) {
    message = error.response.data.message;
  } else if (error?.message) {
    message = error.message;
  }
  
  showError(message, {
    autoClose: 5000,
  });
};

export default {
  success: showSuccess,
  error: showError,
  warning: showWarning,
  info: showInfo,
  loading: showLoading,
  update: updateToast,
  dismiss: dismissToast,
  dismissAll: dismissAllToasts,
  otp: {
    success: showOTPSuccess,
    error: showOTPError,
    cooldown: showOTPCooldown,
    verificationSuccess: showOTPVerificationSuccess,
    verificationError: showOTPVerificationError,
  },
  auth: {
    loginSuccess: showLoginSuccess,
    registrationSuccess: showRegistrationSuccess,
    logoutSuccess: showLogoutSuccess,
  },
  network: {
    error: showNetworkError,
  },
  api: {
    error: showAPIError,
  },
};
