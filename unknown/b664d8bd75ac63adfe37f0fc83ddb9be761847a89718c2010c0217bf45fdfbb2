import React from "react";
import { useSelector } from "react-redux";
import { selectMyDownloads } from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import { FaDownload, FaFilePdf, FaVideo } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/BuyerDownloads.css";

const BuyerDownloads = () => {
  const downloads = useSelector(selectMyDownloads);

  // Function to get file type icon
  const getFileTypeIcon = (fileType) => {
    switch (fileType.toLowerCase()) {
      case "pdf":
        return (
          <FaFilePdf className="BuyerDownloads__file-icon BuyerDownloads__file-icon--pdf" />
        );
      case "video":
        return (
          <FaVideo className="BuyerDownloads__file-icon BuyerDownloads__file-icon--video" />
        );
      default:
        return <FaFilePdf className="BuyerDownloads__file-icon" />;
    }
  };

  const columns = [
    {
      key: "fileType",
      label: "Type",
      render: (item) => getFileTypeIcon(item.fileType),
    },
    { key: "title", label: "Title" },
    { key: "downloadDate", label: "Download Date" },
    {
      key: "action",
      label: "Action",
      render: () => (
        <button className="BuyerDownloads__download-btn">
          <FaDownload /> Download
        </button>
      ),
    },
  ];

  return (
    <div className="BuyerDownloads">
      <SectionWrapper
        icon={<FaDownload className="BuyerSidebar__icon" />}
        title="My Downloads"
      >
        {downloads.length > 0 ? (
          <Table
            columns={columns}
            data={downloads}
            className="BuyerDownloads__table"
            emptyMessage="You have no downloads yet."
          />
        ) : (
          <div className="BuyerDownloads__empty">
            <p>You have no downloads yet.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerDownloads;
