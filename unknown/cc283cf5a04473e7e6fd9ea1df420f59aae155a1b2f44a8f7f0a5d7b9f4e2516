.navbar-component {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px;
  background-color: var(--white);
  box-shadow: var(--box-shadow-light);
  position: sticky;
  top: 0;
  z-index: var(--z-index-header);
}

.navbar-component .navbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin: 0 auto;
}

.navbar-component .navbar-logo {
  display: flex;
  align-items: center;
}

.navbar-component .navbar-logo img {
  height: 60px;
  width: 127px;
}

.navbar-component .navbar-links {
  display: flex;
  gap: 25px;
}

.navbar-component .navbar-links a {
  text-decoration: none;
  color: var(--secondary-color);
  font-weight: 500;
  font-size: var(--basefont);
  transition: color 0.3s ease;
}

.navbar-component .navbar-links a:hover {
  color: var(--btn-color);
}

.navbar-component .navbar-links a.active {
  color: var(--btn-color);
  font-weight: 600;
}

.navbar-component .navbar-auth {
  display: flex;
  gap: 10px;
}

.navbar-component .navbar-toggle {
  display: none;
  background: none;
  border: none;
  font-size: 24px;
  color: var(--secondary-color);
  cursor: pointer;
}

/* Responsive styles */
@media (max-width: 768px) {
  .navbar-component .navbar-toggle {
    display: block;
    background-color: var(--btn-color);
    border-radius: 5px;
    padding: 0;
    color: white !important;
    width: 35px;
    height: 35px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .navbar-component .navbar-links {
    display: none;
  }

  .navbar-component .navbar-auth {
    display: none;
  }
}
@media (max-width: 500px) {
  .navbar-component .navbar-logo img {
    width: 75px;
  }
  .navbar-component {
    padding: 15px 20px 15px 20px;
  }
}
