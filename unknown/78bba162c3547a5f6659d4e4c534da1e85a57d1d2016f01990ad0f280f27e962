import React from "react";
import { useSelector } from "react-redux";
import { selectMyBids } from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import Table from "../../components/common/Table";
import "../../styles/BuyerBids.css";
import { FaGavel } from "react-icons/fa";

const BuyerBids = () => {
  const bids = useSelector(selectMyBids);

  // Function to get status class
  const getStatusClass = (status) => {
    switch (status) {
      case "active":
        return "BuyerBids__status--active";
      case "won":
        return "BuyerBids__status--won";
      case "lost":
        return "BuyerBids__status--lost";
      default:
        return "";
    }
  };

  const columns = [
    { key: "title", label: "Title" },
    { key: "coach", label: "Coach" },
    {
      key: "bidAmount",
      label: "Bid Amount",
      render: (item) => `$${item.bidAmount.toFixed(2)}`,
    },
    { key: "date", label: "Date" },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <div
          className={`BuyerBids__item-status ${getStatusClass(item.status)}`}
        >
          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
        </div>
      ),
    },
  ];

  return (
    <div className="BuyerBids">
      <SectionWrapper
        icon={<FaGavel className="BuyerSidebar__icon" />}
        title="My Bids"
      >
        {bids.length > 0 ? (
          <Table
            columns={columns}
            data={bids}
            className="BuyerBids__table"
            emptyMessage="You have no bids yet."
          />
        ) : (
          <div className="BuyerBids__empty">
            <p>You have no bids yet.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerBids;
