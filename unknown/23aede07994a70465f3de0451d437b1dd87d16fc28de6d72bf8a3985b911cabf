import React from "react";
import { useSelector } from "react-redux";
import { selectMyRequests } from "../../redux/slices/buyerDashboardSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import { FaPlus } from "react-icons/fa";
import Table from "../../components/common/Table";
import "../../styles/BuyerRequests.css";
import { MdRequestPage } from "react-icons/md";

const BuyerRequests = () => {
  const requests = useSelector(selectMyRequests);

  // Function to get status class
  const getStatusClass = (status) => {
    switch (status) {
      case "pending":
        return "BuyerRequests__status--pending";
      case "approved":
        return "BuyerRequests__status--approved";
      case "completed":
        return "BuyerRequests__status--completed";
      default:
        return "";
    }
  };

  const columns = [
    { key: "title", label: "Title" },
    { key: "description", label: "Description" },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <div
          className={`BuyerRequests__item-status ${getStatusClass(
            item.status
          )}`}
        >
          {item.status.charAt(0).toUpperCase() + item.status.slice(1)}
        </div>
      ),
    },
    {
      key: "date",
      label: "Requested On",
      render: (item) => `Requested on: ${item.date}`,
    },
  ];

  return (
    <div className="BuyerRequests">
      <SectionWrapper
        icon={<MdRequestPage className="BuyerSidebar__icon" />}
        title="My Requests"
      >
        <div className="BuyerRequests__header">
          <button className="BuyerRequests__add-btn">
            <FaPlus /> New Request
          </button>
        </div>

        {requests.length > 0 ? (
          <Table
            columns={columns}
            data={requests}
            className="BuyerRequests__table"
            emptyMessage="You have no requests yet."
          />
        ) : (
          <div className="BuyerRequests__empty">
            <p>You have no requests yet.</p>
          </div>
        )}
      </SectionWrapper>
    </div>
  );
};

export default BuyerRequests;
