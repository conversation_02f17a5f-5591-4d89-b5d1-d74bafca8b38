import React from "react";
import { Link } from "react-router-dom";
import "../../styles/StrategyCard.css";
import { FaPlay } from "react-icons/fa";

const StrategyCard = ({
  image,
  title,
  coach,
  price,
  hasVideo,
  id,
  type = "buy",
}) => {
  return (
    <div className="strategy-card-component strategy-card">
      <div className="strategy-card-image">
        <img src={image} alt={title} />
        {hasVideo && (
          <div className="video-icon">
            <FaPlay />
          </div>
        )}
      </div>
      <div className="strategy-card-content">
        <h3 className="strategy-card-title">{title}</h3>
        <p className="strategy-card-coach">By {coach}</p>
        <div className="strategy-card-footer">
          <span className="strategy-card-price">${price.toFixed(2)}</span>
          <Link to={`/buyer/details/${id}`} className={`action-button `}>
            {type === "bid" ? "Bid Now" : "Buy Now"}
          </Link>
        </div>
      </div>
    </div>
  );
};

export default StrategyCard;
