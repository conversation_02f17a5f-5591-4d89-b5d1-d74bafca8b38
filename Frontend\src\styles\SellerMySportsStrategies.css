/* SellerMySportsStrategies.css */

.video-status-container {
  padding: var(--section-padding);
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
}

.video-status-container .video-table {
  width: 100%;

  font-size: var(--basefont);
  background-color: var(--white);

  border-radius: var(--border-radius-large);
  overflow: hidden;
}

.video-status-container .video-table th {
  padding: 12px 10px;
  text-align: left;

  vertical-align: middle;
}
.video-status-container .video-table td {
  padding: 12px 10px;
  text-align: left;
  border-top: 1px solid var(--light-gray);
  vertical-align: middle;
}
.video-status-container .video-doc {
  display: flex;
  align-items: center;
  gap: 10px;
}

.video-status-container .video-doc img {
  width: 40px;
  height: 40px;
  border-radius: var(--border-radius);
  object-fit: cover;
}

.video-status-container .video-doc span {
  font-size: var(--basefont);
  font-weight: 500;
  color: var(--text-color);
}

/* Toggle Switch */
.video-status-container .switch {
  position: relative;
  display: inline-block;
  width: 40px;
  height: 22px;
}

.video-status-container .switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.video-status-container .slider {
  position: absolute;
  cursor: pointer;
  background-color: var(--light-gray);
  border-radius: 22px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  transition: 0.4s;
}

.video-status-container .slider::before {
  position: absolute;
  content: "";
  height: 16px;
  width: 16px;
  left: 3px;
  bottom: 3px;
  background-color: var(--white);
  border-radius: 50%;
  transition: 0.4s;
}

.video-status-container input:checked + .slider {
  background-color: var(--btn-color);
}

.video-status-container input:checked + .slider::before {
  transform: translateX(18px);
}

.video-status-container .slider.round {
  border-radius: 34px;
}

 .eyeicon {
  font-size: var(--heading6) !important;
  color: black;
  cursor: pointer;
  transition: all 0.2s ease;
  
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
}

 .eyeicon:hover {
  color: var(--primary-color);
  background-color: var(--primary-light-color);
  transform: translateY(-2px);
}
