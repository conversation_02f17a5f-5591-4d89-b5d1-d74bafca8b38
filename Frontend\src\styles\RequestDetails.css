/* RequestDetails Component Styles */
.RequestDetails {
  padding: 0;
  background-color: var(--white);
  font-family: "Poppins", sans-serif;
  color: var(--text-color);
}

.RequestDetails__content {
  display: flex;
  flex-direction: column;
  gap: var(--heading4);
}

/* Header Section */
.RequestDetails__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--heading5) 0;
 
  margin-bottom: var(--heading4);
}

.RequestDetails__content-info {
  display: flex;
  align-items: center;
  gap: var(--basefont);
}

.RequestDetails__content-image {
  width: 80px;
  height: 60px;
  object-fit: cover;
  border-radius: var(--border-radius);
}

.RequestDetails__content-details {
  flex: 1;
}

.RequestDetails__content-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
  margin: 0 0 var(--extrasmallfont) 0;
  line-height: 1.4;
}

.RequestDetails__content-subtitle {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
  margin: 0;
}

/* Main Section */
.RequestDetails__main-section {
 
  padding: var(--heading5);

}

.RequestDetails__info-grid {
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--heading4);
  margin-top: var(--heading4);
}

.RequestDetails__info-section,
.RequestDetails__customer-section {
  display: flex;
  justify-content: space-around;
  gap: var(--basefont);
}

.RequestDetails__section-title {
  font-size: var(--heading6);
  color: var(--text-color);
  font-weight: 600;
 
  

}

.vertical-line {
  width: 1px;
  background-color: var(--light-gray);
  height: 100%;
  margin: 0 20px;
}
.RequestDetails__info-item-grid{
  width: 40%;
}
.requestDetails-btn-grid{
  display: flex;
  align-items: center;
}
.RequestDetails__info-item {
  display: flex;
  
  justify-content: space-between;
  align-items: center;
  padding: var(--smallfont) 0;
  border-bottom: 1px solid #f5f5f5;
}

.RequestDetails__info-label {
  font-size: var(--basefont);
  color: var(--dark-gray);
  font-weight: 500;
}

.RequestDetails__info-value {
  font-size: var(--basefont);
  color: var(--text-color);
  font-weight: 600;
}

/* Action Buttons */
.RequestDetails__actions {
padding: 0 0 0 var(--heading4);
  display: flex;
  flex-wrap: wrap;
  gap: var(--smallfont);
  
}

.RequestDetails__btn {
  padding: var(--smallfont) var(--basefont);
  border: none;
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 100px;
}

.RequestDetails__btn--accept {
  background-color: #28a745;
  color: white;
}

.RequestDetails__btn--accept:hover {
  background-color: #218838;
}

.RequestDetails__btn--reject {
  background-color: #dc3545;
  color: white;
}

.RequestDetails__btn--reject:hover {
  background-color: #c82333;
}

.RequestDetails__btn--counter {
  background-color: #ffc107;
  color: #212529;
}

.RequestDetails__btn--counter:hover {
  background-color: #e0a800;
}

.RequestDetails__btn--message {
  background-color: #fddcdc;
  color: var(--text-color);
  border: 1px solid #f8d7da;
}

.RequestDetails__btn--message:hover {
  background-color: #f5c6cb;
}

/* History Section */
.RequestDetails__history-section {
  background-color: var(--white);

  padding: var(--heading5);

}

.RequestDetails__history-table {
  margin-top: var(--basefont);
}

/* Status Badges */
.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: var(--smallfont);
  font-weight: 500;
  text-align: center;
  min-width: 80px;
  display: inline-block;
}

.status-pending {
  background-color: #fff3cd;
  color: #856404;
}

.status-accepted {
  background-color: #d4edda;
  color: #155724;
}

.status-rejected {
  background-color: #f8d7da;
  color: #721c24;
}

.status-counter-offer {
  background-color: #d1ecf1;
  color: #0c5460;
}

/* Action Icons in Table */
.action-icons {
 
  display: flex;
  gap: var(--extrasmallfont);
  justify-content: center;
}

.action-btn {
  font-size: var(--heading6);
  background: none;
  border: none;
  cursor: pointer;
 
  border-radius: 4px;
  transition: background-color 0.3s ease;
}

.action-btn:hover {
  background-color: var(--light-gray);
}




/* Responsive Design */
@media (max-width: 768px) {
  .RequestDetails__info-item {
    flex-direction: column;
    align-items: flex-start;
  }
  .RequestDetails__info-grid {
    grid-template-columns: 1fr;
    gap: var(--heading5);
  }

  .RequestDetails__content-info {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--smallfont);
  }

  .RequestDetails__content-image {
    width: 100%;
    height: 120px;
  }

  .RequestDetails__actions {
    flex-direction: column;
  }

  .RequestDetails__btn {
    width: 100%;
    text-align: center;
  }

  .RequestDetails__main-section,
  .RequestDetails__history-section {
    padding: var(--basefont);
  }

  .RequestDetails__section-title {
    font-size: var(--basefont);
  }

  .RequestDetails__content-title {
    font-size: var(--basefont);
  }

  .RequestDetails__content-subtitle,
  .RequestDetails__info-label,
  .RequestDetails__info-value {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .RequestDetails__info-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }

  .RequestDetails__main-section,
  .RequestDetails__history-section {
    padding: var(--smallfont);
  }

  .RequestDetails__section-title {
    font-size: var(--smallfont);
    margin-bottom: var(--smallfont);
  }

  .RequestDetails__content-title {
    font-size: var(--smallfont);
  }

  .RequestDetails__content-subtitle,
  .RequestDetails__info-label,
  .RequestDetails__info-value {
    font-size: var(--extrasmallfont);
  }

  .RequestDetails__btn {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--extrasmallfont);
  }

  .status-badge {
    font-size: var(--extrasmallfont);
    padding: 2px 6px;
    min-width: 60px;
  }
}

@media (max-width: 400px) {
  .RequestDetails__info-section, .RequestDetails__customer-section{
    flex-direction: column;
    gap:0px;
    
  }
  .RequestDetails__section-title{
   
    margin-top: var(--smallfont);
    margin-bottom: 0px;
  }
}