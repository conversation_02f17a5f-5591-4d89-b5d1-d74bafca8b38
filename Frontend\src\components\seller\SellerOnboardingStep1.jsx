import React, { useState, useEffect } from 'react';
import { FaTrash } from 'react-icons/fa';
import './SellerOnboardingStep1.css';

const SellerOnboardingStep1 = ({ formData, onInputChange, onExperienceChange, onAddExperience, onRemoveExperience, onNext, fieldErrors }) => {
  const [previewUrl, setPreviewUrl] = useState(formData.profilePic || null);
  const [imageError, setImageError] = useState('');

  // Supported image formats
  const SUPPORTED_IMAGE_FORMATS = ['jpg', 'jpeg', 'png'];
  const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB

  // Sync previewUrl with formData.profilePic changes
  useEffect(() => {
    if (formData.profilePic && formData.profilePic !== previewUrl) {
      setPreviewUrl(formData.profilePic);
    }
  }, [formData.profilePic, previewUrl]);

  // Validate image file
  const validateImageFile = (file) => {
    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      return 'File size must be less than 10MB';
    }

    // Check file type
    const fileExtension = file.name.split('.').pop().toLowerCase();
    if (!SUPPORTED_IMAGE_FORMATS.includes(fileExtension)) {
      return `Unsupported file format. Please use: ${SUPPORTED_IMAGE_FORMATS.join(', ').toUpperCase()}`;
    }

    // Check MIME type
    const supportedMimeTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!supportedMimeTypes.includes(file.type)) {
      return `Invalid file type. Please select a valid image file.`;
    }

    return null; // No errors
  };

  const handleFileSelect = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Clear previous errors
      setImageError('');

      // Validate the file
      const validationError = validateImageFile(file);
      if (validationError) {
        setImageError(validationError);
        // Clear the file input
        e.target.value = '';
        return;
      }

      // Create preview URL for immediate display
      const reader = new FileReader();
      reader.onload = (e) => {
        setPreviewUrl(e.target.result);
      };
      reader.readAsDataURL(file);

      // Store the selected file in the parent component for later upload
      onInputChange('selectedImageFile', file);

      console.log('Image selected for later upload:', file.name);
    }
  };



  return (
    <div className="seller-onboarding-step1-container max-container">
      {/* Progress Bar */}
      <div className="progress-bar">
        <div className="step active">1</div>
        <div className="progress-line" />
        <div className="step">2</div>
      </div>

      <div className="form-grid">
        {/* Description Section */}
        <div className="description-section">
          <div className="section-title">Description</div>
          <div className="description-box">
            <textarea
              className={`description-textarea ${fieldErrors?.description ? 'error' : ''}`}
              placeholder="Write Description.."
              rows={3}
              value={formData.description}
              onChange={e => onInputChange('description', e.target.value)}
            />
            {fieldErrors?.description && (
              <div className="field-error">{fieldErrors.description}</div>
            )}
          </div>
        </div>

        {/* Profile Pic & Experience Section */}
        <div className="profile-experience-grid">
          {/* Profile Pic */}
          <div className="profile-pic-section">
            <div className="section-title">Profile Pic</div>
            <div className="avatar-upload">
              <div className="avatar-placeholder">
                {previewUrl || formData.profilePic ? (
                  <img
                    src={previewUrl || formData.profilePic}
                    alt="Profile"
                    className="avatar-image"
                  />
                ) : (
                  <svg width="64" height="64" viewBox="0 0 64 64" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <circle cx="32" cy="32" r="32" fill="var(--light-gray)" />
                    <ellipse cx="32" cy="27" rx="12" ry="12" fill="#fff" />
                    <ellipse cx="32" cy="50" rx="16" ry="10" fill="#fff" />
                  </svg>
                )}
              </div>
              <input
                type="file"
                id="profilePicInput"
                accept="image/jpeg,image/jpg,image/png,image/gif"
                onChange={handleFileSelect}
                style={{ display: 'none' }}
              />
              <div className="upload-buttons">
                <button
                  type="button"
                  className="btn btn-outline upload-btn"
                  onClick={() => document.getElementById('profilePicInput').click()}
                >
                  Choose Photo
                </button>
              </div>
              {/* Display supported formats */}
              <div className="upload-info">
                <small className="upload-format-info">
                  Supported formats: {SUPPORTED_IMAGE_FORMATS.join(', ').toUpperCase()} (Max: 10MB)
                </small>
              </div>
              {/* Display validation errors */}
              {imageError && (
                <div className="field-error image-error">{imageError}</div>
              )}
            </div>
          </div>

          {/* Experience */}
          <div className="experience-section">
            <div className="section-title">Experience</div>
            <div className="experience-container">
              {formData.experiences.map((exp, idx) => (
                <div className="experience-row" key={idx}>
                  <div className="experience-row-content">
                    <input
                      type="text"
                      className={`input ${fieldErrors?.experiences && idx === 0 ? 'error' : ''}`}
                      placeholder="Enter School Name"
                      value={exp.schoolName}
                      onChange={e => onExperienceChange(idx, 'schoolName', e.target.value)}
                    />
                    <input
                      type="text"
                      className="input"
                      placeholder="Enter Position"
                      value={exp.position}
                      onChange={e => onExperienceChange(idx, 'position', e.target.value)}
                    />
                    <div className="year-fields">

                      <div>
                        <input
                          type="text"
                          className={`input year-input ${fieldErrors?.experienceYears?.[idx]?.fromYear ? 'error' : ''}`}
                          placeholder="From Year"
                          value={exp.fromYear}
                          onChange={e => onExperienceChange(idx, 'fromYear', e.target.value)}
                        />
                        {fieldErrors?.experienceYears?.[idx]?.fromYear && (
                          <div className="field-error">{fieldErrors.experienceYears[idx].fromYear}</div>
                        )}
                      </div>

                      <div>
                        <input
                          type="text"
                          className={`input year-input ${fieldErrors?.experienceYears?.[idx]?.toYear ? 'error' : ''}`}
                          placeholder="To Year"
                          value={exp.toYear}
                          onChange={e => onExperienceChange(idx, 'toYear', e.target.value)}
                        />
                        {fieldErrors?.experienceYears?.[idx]?.toYear && (
                          <div className="field-error">{fieldErrors.experienceYears[idx].toYear}</div>
                        )}
                      </div>
                    </div>
                  </div>
                  {/* Delete button - only show when there are 2 or more experiences */}
                  {formData.experiences.length > 1 && onRemoveExperience && (
                    <button
                      type="button"
                      className="delete-experience-btn"
                      onClick={() => onRemoveExperience(idx)}
                      title="Remove this experience"
                    >
                      <FaTrash />
                    </button>
                  )}
                </div>
              ))}
              {fieldErrors?.experiences && (
                <div className="field-error">{fieldErrors.experiences}</div>
              )}
              <div className="add-more-link" onClick={onAddExperience}>
                + Add More
              </div>
            </div>
          </div>
        </div>
      </div>



      {/* Next Button */}
      <div className="next-btn-row">
        <button className="btn btn-primary next-btn" onClick={onNext}>Next</button>
      </div>
    </div>
  );
};

export default SellerOnboardingStep1;