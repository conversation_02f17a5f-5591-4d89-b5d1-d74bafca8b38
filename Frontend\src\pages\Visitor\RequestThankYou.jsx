import React from "react";
import { useNavigate } from "react-router-dom";
import "../../styles/RequestThankYou.css";
import thankyouIcon from "../../assets/images/thankyou.svg";
import itemImage from "../../assets/images/herosideimg.svg";

const RequestThankYou = () => {
  const navigate = useNavigate();

  // Mock data - in real implementation this would come from props or state
  const requestData = {
    requestId: "#********",
    price: "$22.00",
    requestedAmount: "$15.00",
    date: "20 May 2025 | 4:50PM",
    itemTitle: "<PERSON> and Coaching Philosophies to Developing Toughness In Your Players",
    itemAuthor: "By Basketball Coaches Clinic"
  };

  const handleGoToRequests = () => {
    navigate("/buyer/account/requests");
  };

  const handleGoToHomepage = () => {
    navigate("/");
  };

  return (
    <div className="request-thank-you-page">
      <div className="request-thank-you-container max-container">
        {/* Success Header */}
        <div className="success-header">
          <div className="success-icon">
            <img src={thankyouIcon} alt="Thank you" />
          </div>
          <h1 className="success-title">Your request is submitted successfully!</h1>
          <p className="success-message">
            We will update you for the request status soon via Email or SMS.
          </p>
        </div>

        {/* Request Information Card */}
        <div className="request-info-card">
          <h2 className="request-info-title">Request Information</h2>

        <div className="reqthankyoucontainer">
            <div className="request-details-grid">
            <div className="request-detail-item">
              <span className="detail-label">Request Id</span>
              <span className="detail-value">{requestData.requestId}</span>
            </div>
            <div className="request-detail-item">
              <span className="detail-label">Price</span>
              <span className="detail-value">{requestData.price}</span>
            </div>
            </div>
            <div className="verticalline"></div>
             <div className="request-details-grid">
            <div className="request-detail-item">
              <span className="detail-label">Date</span>
              <span className="detail-value">{requestData.date}</span>
            </div>
            <div className="request-detail-item">
              <span className="detail-label">Requested Amount</span>
              <span className="detail-value">{requestData.requestedAmount}</span>
            </div>
          </div>
        </div>

          {/* Item Info Section */}
          <div className="item-info-section">
            <h3 className="section-title">Item Info</h3>
            <div className="item-info-content">
              <img src={itemImage} alt="Item" className="item-image" />
              <div className="item-details">
                <p className="item-title">{requestData.itemTitle}</p>
                <p className="item-author">{requestData.itemAuthor}</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="action-buttons">
            <button
              type="button"
              className="btn btn-outline request-btn"
              onClick={handleGoToRequests}
            >
              Go To My Request Page
            </button>
            <button
              type="button"
              className="btn btn-primary homepage-btn"
              onClick={handleGoToHomepage}
            >
              Go To Homepage
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequestThankYou;
