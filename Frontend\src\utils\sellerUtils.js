/**
 * Utility functions for seller-related operations
 */

/**
 * Check if seller needs to complete onboarding
 * @param {Object} user - User object from Redux state or localStorage
 * @returns {boolean} True if seller needs onboarding, false otherwise
 */
export const needsSellerOnboarding = (user) => {
  // Only check for sellers
  if (!user || user.role !== 'seller') {
    return false;
  }

  // Check if onboarding is complete
  const sellerInfo = user.sellerInfo || {};
  return !sellerInfo.isOnboardingComplete;
};

/**
 * Get the appropriate redirect path for a seller based on onboarding status
 * @param {Object} user - User object from Redux state or localStorage
 * @returns {string} Redirect path
 */
export const getSellerRedirectPath = (user) => {
  if (!user || user.role !== 'seller') {
    return '/seller/dashboard';
  }

  // If onboarding is not complete, redirect to onboarding
  if (needsSellerOnboarding(user)) {
    return '/seller-onboarding';
  }

  // Otherwise, redirect to dashboard
  return '/seller/dashboard';
};

/**
 * Check if current route requires seller onboarding to be complete
 * @param {string} pathname - Current route pathname
 * @returns {boolean} True if route requires completed onboarding
 */
export const requiresOnboardingComplete = (pathname) => {
  // Routes that require onboarding to be complete
  const protectedSellerRoutes = [
    '/seller/dashboard',
    '/seller/profile',
    '/seller/requests',
    '/seller/earnings',
    '/seller/settings'
  ];

  return protectedSellerRoutes.some(route => pathname.startsWith(route));
};
