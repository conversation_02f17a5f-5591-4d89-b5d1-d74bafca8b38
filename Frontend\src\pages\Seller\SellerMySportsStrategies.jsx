import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import SellerLayout from "../../components/seller/SellerLayout";
import { IoEyeSharp } from "react-icons/io5";
import Table from "../../components/common/Table";
import "../../styles/SellerMySportsStrategies.css";
import { SlEye } from "react-icons/sl";

const initialData = [
  {
    id: 1,
    title: "<PERSON> and Coaching Philosophies...",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-1.jpg",
  },
  {
    id: 2,
    title: "<PERSON> - Early Transition Offensive Concepts",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-2.jpg",
  },
  {
    id: 3,
    title: "WR Fundamentals\nPoA - <PERSON>",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-3.jpg",
  },
  {
    id: 4,
    title: "<PERSON> and Coaching Philosophies...",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-1.jpg",
  },
  {
    id: 5,
    title: "John Calipari - Early Transition Offensive Concepts",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-2.jpg",
  },
  {
    id: 6,
    title: "WR Fundamentals\nPoA - Herman Wiggins",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-3.jpg",
  },
  {
    id: 7,
    title: "Frank Martin - Drills and Coaching Philosophies...",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: true,
    thumbnail: "video-1.jpg",
  },
  {
    id: 8,
    title: "John Calipari - Early Transition Offensive Concepts",
    date: "20 May 2025 | 4:50PM",
    price: "$22.00",
    status: false,
    thumbnail: "video-2.jpg",
  },
];

const SellerMySportsStrategies = () => {
  const [videos, setVideos] = useState(initialData);
  const navigate = useNavigate();

  const toggleStatus = (id) => {
    setVideos((prev) =>
      prev.map((item) =>
        item.id === id ? { ...item, status: !item.status } : item
      )
    );
  };

  const handleViewDetails = (id) => {
    navigate(`/seller/strategy-details/${id}`);
  };

  const columns = [
    { key: "no", label: "No.", className: "no" },
    {
      key: "content",
      label: "Videos/Documents",
      render: (item) => (
        <div className="video-doc">
          <img src={item.thumbnail} alt="video thumb" />
          <span>{item.title}</span>
        </div>
      ),
    },
    { key: "date", label: "Date" },
    { key: "price", label: "Price" },
    {
      key: "status",
      label: "Status",
      render: (item) => (
        <label className="switch">
          <input
            type="checkbox"
            checked={item.status}
            onChange={() => toggleStatus(item.id)}
          />
          <span className="slider round"></span>
        </label>
      ),
    },
    {
      key: "action",
      label: "Action",
      render: (item) => (
        <div className="action-icon-container">
          <SlEye
          className="eyeicon"
          onClick={() => handleViewDetails(item.id)}
        />
        </div>
      ),
    },
  ];

  const formatData = (videos) => {
    return videos.map((item, index) => ({
      ...item,
      no: index + 1,
    }));
  };

  return (
    <SellerLayout>
      <div className="video-status-container">
        <Table
          columns={columns}
          data={formatData(videos)}
          className="video-table"
        />
      </div>
    </SellerLayout>
  );
};

export default SellerMySportsStrategies;
