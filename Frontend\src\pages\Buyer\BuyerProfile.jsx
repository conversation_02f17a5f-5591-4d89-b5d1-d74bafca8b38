import React, { useState, useEffect } from "react";
import { useSelector, useDispatch } from "react-redux";
import {
  getCurrentUser,
  updateCurrentUser,
  uploadProfileImage,
  reset,
} from "../../redux/slices/authSlice";
import SectionWrapper from "../../components/common/SectionWrapper";
import { FaUser, FaEnvelope, FaPhone } from "react-icons/fa";
import { getImageUrl } from "../../utils/constants";
import toast from "../../utils/toast";
import "../../styles/BuyerProfile.css";

const BuyerProfile = () => {
  const dispatch = useDispatch();
  const { user, isLoading, isSuccess, isError, error } = useSelector(
    (state) => state.auth
  );

  // Local state for form inputs
  const [formData, setFormData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    mobile: "",
    profileImage: "",
  });

  const [isFormLoading, setIsFormLoading] = useState(true);
  const [selectedFile, setSelectedFile] = useState(null);
  const [previewImage, setPreviewImage] = useState(null);

  // Fetch user data on component mount
  useEffect(() => {
    dispatch(getCurrentUser());
  }, [dispatch]);

  // Update form data when user data is loaded
  useEffect(() => {
    if (user) {
      setFormData({
        firstName: user.firstName || "",
        lastName: user.lastName || "",
        email: user.email || "",
        mobile: user.mobile || "",
        profileImage: user.profileImage || "",
      });
      setImageError(false); // Reset image error when user data changes
      setIsFormLoading(false);
    }
  }, [user]);

  // Handle success/error states - only for form submission
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [imageError, setImageError] = useState(false);

  useEffect(() => {
    if (isSubmitting && isSuccess && !isLoading) {
      toast.success("Profile updated successfully!");
      dispatch(reset());
      setIsSubmitting(false);
      // Refresh user data to get updated profile image
      dispatch(getCurrentUser());
    }

    if (isSubmitting && isError && error) {
      toast.error(error.message || "Failed to update profile");
      dispatch(reset());
      setIsSubmitting(false);
    }
  }, [isSuccess, isError, error, isLoading, dispatch, isSubmitting]);

  // Handle input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      let profileImageUrl = formData.profileImage;

      // Upload image first if a new file is selected
      if (selectedFile) {
        const uploadResult = await dispatch(
          uploadProfileImage(selectedFile)
        ).unwrap();
        profileImageUrl = uploadResult.data.fileUrl;
      }

      // Only send editable fields
      const updateData = {
        firstName: formData.firstName,
        lastName: formData.lastName,
        profileImage: profileImageUrl,
      };

      dispatch(updateCurrentUser(updateData));
    } catch (error) {
      toast.error("Failed to upload image or update profile");
      setIsSubmitting(false);
    }
  };

  // Handle profile image upload
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      setSelectedFile(file);
      setImageError(false); // Reset image error when new file is selected

      // Create preview
      const reader = new FileReader();
      reader.onloadend = () => {
        setPreviewImage(reader.result);
      };
      reader.readAsDataURL(file);
    }
  };

  // Handle image load error
  const handleImageError = () => {
    setImageError(true);
  };

  // Handle account deletion
  const handleDeleteAccount = () => {
    // Show confirmation dialog and handle account deletion
    if (
      window.confirm(
        "Are you sure you want to delete your account? This action cannot be undone."
      )
    ) {
      // Handle account deletion logic here
    }
  };

  return (
    <div className="BuyerProfile">
      <SectionWrapper
        icon={<FaUser className="BuyerSidebar__icon" />}
        title="My Profile"
      >
        <div className="profile_border_container">
          <div className="BuyerProfile__container">
            <div className="BuyerProfile__left-section">
              <div className="BuyerProfile__form-row">
                <div className="BuyerProfile__input-field">
                  <div className="BuyerProfile__input-container">
                    <div className="BuyerProfile__input-icon">
                      <FaUser />
                    </div>
                    <input
                      type="text"
                      id="firstName"
                      name="firstName"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      placeholder="First Name"
                      required
                      className="BuyerProfile__input"
                    />
                  </div>
                </div>

                <div className="BuyerProfile__input-field">
                  <div className="BuyerProfile__input-container">
                    <div className="BuyerProfile__input-icon">
                      <FaUser />
                    </div>
                    <input
                      type="text"
                      id="lastName"
                      name="lastName"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      placeholder="Last Name"
                      required
                      className="BuyerProfile__input"
                    />
                  </div>
                </div>
              </div>

              <div className="BuyerProfile__input-field">
                <div className="BuyerProfile__input-container">
                  <div className="BuyerProfile__input-icon">
                    <FaEnvelope />
                  </div>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    placeholder="Email Address"
                    className="BuyerProfile__input BuyerProfile__input--readonly"
                    readOnly
                    disabled
                  />
                </div>
              </div>

              <div className="BuyerProfile__input-field">
                <div className="BuyerProfile__input-container">
                  <div className="BuyerProfile__input-icon">
                    <FaPhone />
                  </div>
                  <input
                    type="tel"
                    id="mobile"
                    name="mobile"
                    value={formData.mobile}
                    placeholder="Mobile Number"
                    className="BuyerProfile__input BuyerProfile__input--readonly"
                    readOnly
                    disabled
                  />
                </div>
              </div>
            </div>

            <div className="BuyerProfile__right-section">
              <div className="BuyerProfile__image-container">
                <h3 className="BuyerProfile__image-title">Profile Image</h3>
                <div className="BuyerProfile__image">
                  {previewImage || (formData.profileImage && !imageError) ? (
                    <img
                      src={previewImage || getImageUrl(formData.profileImage)}
                      alt="Profile"
                      onError={handleImageError}
                    />
                  ) : (
                    <div className="BuyerProfile__placeholder">
                      {formData.firstName && formData.lastName ? (
                        `${formData.firstName.charAt(
                          0
                        )}${formData.lastName.charAt(0)}`
                      ) : (
                        <FaUser className="BuyerProfile__user-icon" />
                      )}
                    </div>
                  )}
                </div>
                <button
                  className="BuyerProfile__upload-btn"
                  onClick={() =>
                    document.getElementById("profile-image-upload").click()
                  }
                >
                  Upload Photo
                </button>
                <input
                  type="file"
                  id="profile-image-upload"
                  accept="image/*"
                  onChange={handleImageUpload}
                  style={{ display: "none" }}
                />
              </div>
            </div>
          </div>

          <div className="BuyerProfile__buttons mt-30">
            <button
              type="button"
              className="BuyerProfile__delete-btn"
              onClick={handleDeleteAccount}
            >
              Delete Account
            </button>

            <button
              type="button"
              className="BuyerProfile__save-btn"
              onClick={handleSubmit}
              disabled={isSubmitting || isLoading}
            >
              {isSubmitting || isLoading ? "Updating..." : "Update & Save"}
            </button>
          </div>
        </div>
      </SectionWrapper>
    </div>
  );
};

export default BuyerProfile;
