{"name": "xosportshub", "version": "1.0.0", "description": "Digital content marketplace platform for sports training", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "seed": "node seeders/index.js"}, "keywords": ["sports", "training", "marketplace", "content", "ecommerce"], "author": "", "license": "ISC", "type": "commonjs", "dependencies": {"aws-sdk": "^2.1386.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-validator": "^7.0.1", "firebase-admin": "^13.4.0", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.0", "mongoose": "^7.2.0", "morgan": "^1.10.0", "multer": "^2.0.0", "multer-s3": "^3.0.1", "nodemailer": "^6.9.2", "pdfkit": "^0.13.0", "stripe": "^12.6.0", "twilio": "^5.6.1"}, "devDependencies": {"nodemon": "^3.1.10"}}