.SellerProfile {
  display: flex;
  flex-direction: column;
  width: 100%;
  gap: var(--heading5);
}

/* Header Section */
.SellerProfile__header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--heading5);
  padding-bottom: var(--basefont);
  border-bottom: 1px solid var(--light-gray);
}

.SellerProfile__title {
  font-size: var(--heading3);
  font-weight: 700;
  color: var(--secondary-color);
  margin: 0;
}

.SellerProfile__edit-btn {
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 600;
  font-size: var(--smallfont);
}

.SellerProfile__edit-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

/* Section Styling */
.SellerProfile__section {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius-large);
  padding: var(--heading5);
  display:grid;
  gap:var(--basefont);
  margin-bottom: var(--basefont);
}

.SellerProfile__section-title {
  font-size: var(--heading6);
  font-weight: 700;
  color: var(--secondary-color);
  margin: 0 0 var(--basefont) 0;
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.SellerProfile__container {
    display: grid
    ;
        width: 100%;
        justify-content: space-between;
        gap: var(--heading6);
        grid-template-columns: 60% 1fr;
}

.SellerProfile__left-section {
  display: grid;
  align-items: start;
  flex: 1;
  gap: var(--basefont);
}

.SellerProfile__right-section {
  display: flex;
  flex-direction: column;
  align-items: center;


  max-width: 100%;
  padding-left: var(--heading6);
}

.SellerProfile__form-row {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--basefont);
}
.SellerProfile__form-row-email-phone{
  display:flex!important;
}
.SellerProfile__input-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.SellerProfile__input-container {
  display: flex;
  align-items: center;
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: border-color 0.3s, box-shadow 0.3s;
  overflow: hidden;
}

.SellerProfile__input-container:focus-within {
  border-color: var(--btn-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.SellerProfile__input-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--smallfont);
  color: var(--dark-gray);
}

.SellerProfile__input {
  width: 100%;
  padding: var(--smallfont) var(--basefont);
  border: none;
  font-size: var(--basefont);
  color: var(--text-color);
  background-color: transparent;
  outline: none;
}
.SellerProfile__experiences-grid .SellerProfile__input{
  border: 1px solid var(--light-gray);
}

.SellerProfile__input::placeholder {
  color: var(--dark-gray);
  opacity: 0.7;
}

.SellerProfile__input--disabled {
 
  color: var(--dark-gray) !important;
  cursor: not-allowed;
}

.SellerProfile__image-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: var(--basefont);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--light-gray);
  background: var(--white);
  width: 100%;
}

.SellerProfile__image-title {
  display: flex;
  justify-content: center;
  font-size: var(--heading6);
  color: var(--secondary-color);
  padding-bottom: var(--basefont);
  font-weight: 500;
}

.SellerProfile__image {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  overflow: hidden;
  border: 1px solid var(--light-gray);
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--white);
  margin-bottom: var(--basefont);
}

.SellerProfile__image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.SellerProfile__placeholder {
  width: 100%;
  height: 100%;
  background-color: var(--bg-gray);
  color: var(--dark-gray);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--heading4);
  font-weight: 600;
}

.SellerProfile__user-icon {
  font-size: 40px;
  color: var(--dark-gray);
}

.SellerProfile__upload-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--btn-color);
  border: 1px solid var(--btn-color);
  padding: var(--extrasmallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  font-size: var(--smallfont);
}

.SellerProfile__upload-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.SellerProfile__buttons {
  display: flex;
  justify-content: flex-start;
  gap: var(--heading5);
  align-items: center;
  width: 100%;
  margin-top: var(--heading6);
}

.SellerProfile__save-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--btn-color);
  color: var(--white);
  border: 1px solid var(--btn-color);
  padding: var(--smallfont) var(--heading6);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 600;
  font-size: var(--basefont);
  min-width: 150px;
}

.SellerProfile__save-btn:disabled {
  opacity: 0.7;
  cursor: not-allowed;
}

.SellerProfile__save-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.SellerProfile__delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: transparent;
  color: var(--dark-gray);
  border: 1px solid var(--light-gray);
  padding: var(--smallfont) var(--heading6);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-weight: 500;
  font-size: var(--basefont);
  min-width: 150px;
}

.SellerProfile__delete-btn:hover {
  background-color: rgba(0, 0, 0, 0.05);
  border-color: var(--dark-gray);
}

/* Description Section */
.SellerProfile__description-container {
  margin-top: var(--smallfont);
}

.SellerProfile__textarea {
  width: 100%;
  min-height: 100px;
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-family: inherit;
  font-size: var(--smallfont);
  resize: vertical;
  transition: border-color 0.3s;
}

.SellerProfile__textarea:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(238, 52, 37, 0.1);
}

.SellerProfile__description-display {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
  line-height: 1.6;
  color: var(--secondary-color);
  min-height: 60px;
  display: flex;
  align-items: center;
}

/* Experience Section */
.SellerProfile__description-experience-container{
  display:flex;
  gap:var(--basefont);
}

.SellerProfile__section{
  width:100%;
}

.SellerProfile__experience-item {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
  margin-bottom: var(--smallfont);
}

.SellerProfile__experience-header {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-bottom: var(--basefont);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.SellerProfile__experience-number {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  flex: 1;
}

.SellerProfile__experience-content {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.SellerProfile__experience-field {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
}

.SellerProfile__experience-years {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--smallfont);
}

.SellerProfile__experience-years span {
  padding: var(--smallfont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  font-size: var(--basefont);
}

.SellerProfile__experience-icon {
  color: var(--primary-color);
  font-size: var(--basefont);
  margin-top: 2px;
  flex-shrink: 0;
}

.SellerProfile__experience-details {
  flex: 1;
}

.SellerProfile__experience-school {
  font-size: var(--basefont);
  font-weight: 700;
  color: var(--secondary-color);
  margin: 0 0 var(--extrasmallfont) 0;
}

.SellerProfile__experience-position {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin: 0 0 var(--extrasmallfont) 0;
  font-weight: 600;
}

.SellerProfile__experience-duration {
  font-size: var(--extrasmallfont);
  color: var(--primary-color);
  margin: 0;
  font-weight: 600;
}

/* Training Cost Section */
.SellerProfile__cost-container {
  margin-top: var(--smallfont);
}

.SellerProfile__cost-display {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
 border: 1px solid var(--light-gray);
  padding: var(--basefont);
  border-radius: var(--border-radius);
  border-left: 4px solid var(--primary-color);
}

.SellerProfile__cost-icon {
  color: var(--primary-color);
  font-size: var(--basefont);
}

.SellerProfile__cost-amount {
  font-size: var(--basefont);
  font-weight: 700;
  color: var(--secondary-color);
}

/* Social Links Section */
.SellerProfile__social-links {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
  margin-top: var(--smallfont);
}

.SellerProfile__social-item {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  border: 1px solid var(--light-gray);
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
}

.SellerProfile__social-icon {
  font-size: var(--basefont);
  flex-shrink: 0;
}


.SellerProfile__social-label {
  font-weight: 600;
  color: var(--secondary-color);
  min-width: 80px;
  font-size: var(--smallfont);
}

.SellerProfile__social-value {
  flex: 1;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.SellerProfile__social-value a {
  color: var(--primary-color);
  text-decoration: none;
  word-break: break-all;
}

.SellerProfile__social-value a:hover {
  text-decoration: underline;
}

/* No Data Display */
.SellerProfile__no-data {
  text-align: center;
  color: var(--dark-gray);
  font-style: italic;
  padding: var(--heading5);
  background: var(--bg-blue);
  border-radius: var(--border-radius);
  font-size: var(--smallfont);
}

/* Experience Edit Mode Styles */
.SellerProfile__experience-edit-item {
  background: var(--white);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  padding: var(--basefont);
  margin-bottom: var(--smallfont);
}

.SellerProfile__experience-edit-header {
  display: flex;
  align-items: center;
  gap: var(--smallfont);
  margin-bottom: var(--basefont);
  padding-bottom: var(--smallfont);
  border-bottom: 1px solid var(--light-gray);
}

.SellerProfile__experience-number {
  font-weight: 600;
  color: var(--secondary-color);
  font-size: var(--smallfont);
  flex: 1;
}

.SellerProfile__remove-btn {
  background: transparent;
  border: 1px solid var(--error-color);
  color: var(--error-color);
  padding: var(--extrasmallfont) var(--smallfont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-size: var(--extrasmallfont);
  display: flex;
  align-items: center;
  gap: var(--extrasmallfont);
}

.SellerProfile__remove-btn:hover {
  background: var(--error-color);
  color: var(--white);
}

.SellerProfile__experience-form {
  display: flex;
  flex-direction: column;
  gap: var(--smallfont);
}

.SellerProfile__add-btn {
  background: var(--primary-color);
  color: var(--white);
  border: none;
  padding: var(--smallfont) var(--basefont);
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s;
  font-size: var(--smallfont);
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--extrasmallfont);
  margin-top: var(--smallfont);
}

.SellerProfile__add-btn:hover {
  
  transform: translateY(-2px);
 
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

/* Social Media Icon Colors in Edit Mode */
.SellerProfile__input-icon .facebook {
  color: #1877f2;
}

.SellerProfile__input-icon .linkedin {
  color: #0077b5;
}

.SellerProfile__input-icon .twitter {
  color: #1da1f2;
}

/* Validation Error Styles */
.SellerProfile__input--error {
  border-color: #dc2626 !important;
  background-color: #fef2f2;
}

.SellerProfile__input--error:focus {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.SellerProfile__field-error {
  color: #dc2626;
  font-size: var(--extrasmallfont);
  margin-top: var(--extrasmallfont);
  font-weight: 500;
  line-height: 1.4;
}

/* Responsive styles */
@media (max-width: 1024px) {

  .SellerProfile__right-section {
    width: 100%;
    padding-left: 0;
    margin-bottom: var(--heading6);

  }
  .SellerProfile__left-section {
    width: 100%;
    gap: var(--basefont);
  }
}

@media (max-width: 768px) {
    .SellerProfile__container{
        grid-template-columns: 1fr;
    }
  .SellerProfile__right-section {
    width: 100%;
    padding-left: 0;
    margin-bottom: var(--heading6);
    order: -1;
  }
  .SellerProfile__left-section {
    width: 100%;
    gap: var(--basefont);
  }
  .SellerProfile__form-row {
    grid-template-columns: 1fr;
    gap: var(--smallfont);
  }
  .SellerProfile__buttons {
    flex-direction: column;
    gap: var(--smallfont);
    width: 100%;
  }
  .SellerProfile__save-btn,
  .SellerProfile__delete-btn {
    width: 100%;
  }
  .SellerProfile__header {
    flex-direction: column;
    gap: var(--smallfont);
    text-align: center;
  }
  .SellerProfile__social-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--extrasmallfont);
  }
  .SellerProfile__social-label {
    min-width: auto;
  }
  .SellerProfile__experience-edit-header {
    flex-wrap: wrap;
    gap: var(--extrasmallfont);
  }
  .SellerProfile__add-btn {
    font-size: var(--extrasmallfont);
    padding: var(--extrasmallfont) var(--smallfont);
  }
}

@media (max-width: 480px) {
  .SellerProfile__image {
    width: 80px;
    height: 80px;
  }
  .SellerProfile__image-title {
    font-size: var(--basefont);
  }
  .SellerProfile__save-btn,
  .SellerProfile__delete-btn {
    min-width: 100px;
    font-size: var(--smallfont);
    padding: var(--extrasmallfont) var(--basefont);
  }
}

.SellerProfile__experiences{
  display:grid;
  gap:var(--basefont);
}

.SellerProfile__experiences-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.SellerProfile__add-experience-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}

.SellerProfile__experience-edit-item {
  background: #fff;
  border-radius: 8px;
  padding: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@media (max-width: 768px) {
  .SellerProfile__experiences-grid {
    grid-template-columns: 1fr;
  }
}
