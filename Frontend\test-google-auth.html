<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Google Auth Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        button {
            background: #4285f4;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 0;
        }
        button:hover {
            background: #3367d6;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            background: #f8f9fa;
            border-left: 4px solid #4285f4;
        }
        .error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }
        .success {
            border-left-color: #28a745;
            background: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Google Authentication Test</h1>
        <p>This is a simple test to verify Google authentication is working properly.</p>
        
        <button onclick="testGoogleAuth()">Test Google Sign-In</button>
        <button onclick="clearResults()">Clear Results</button>
        
        <div id="result"></div>
    </div>

    <!-- Firebase SDK -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { 
            getAuth, 
            GoogleAuthProvider, 
            signInWithPopup 
        } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';

        // Firebase configuration (using your actual config)
        const firebaseConfig = {
            apiKey: "AIzaSyACPzZAYLOY8FlSjryn7rnIJd3M9VvmFg8",
            authDomain: "xosportshub.firebaseapp.com",
            projectId: "xosportshub",
            storageBucket: "xosportshub.firebasestorage.app",
            messagingSenderId: "668746900562",
            appId: "1:668746900562:web:ae5ed5ff3f55d55b6deb8b"
        };

        // Initialize Firebase
        const app = initializeApp(firebaseConfig);
        const auth = getAuth(app);
        const provider = new GoogleAuthProvider();

        // Configure provider
        provider.addScope('email');
        provider.addScope('profile');

        // Make functions globally available
        window.testGoogleAuth = async function() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>Testing Google authentication...</p>';
            
            try {
                const result = await signInWithPopup(auth, provider);
                const user = result.user;
                const idToken = await user.getIdToken();
                
                resultDiv.innerHTML = `
                    <div class="result success">
                        <h3>✅ Google Authentication Successful!</h3>
                        <p><strong>Name:</strong> ${user.displayName}</p>
                        <p><strong>Email:</strong> ${user.email}</p>
                        <p><strong>UID:</strong> ${user.uid}</p>
                        <p><strong>Email Verified:</strong> ${user.emailVerified}</p>
                        <p><strong>ID Token:</strong> ${idToken.substring(0, 50)}...</p>
                        <p><em>Now testing backend API...</em></p>
                    </div>
                `;
                
                // Test backend API
                await testBackendAPI(idToken);
                
            } catch (error) {
                console.error('Google auth error:', error);
                resultDiv.innerHTML = `
                    <div class="result error">
                        <h3>❌ Google Authentication Failed</h3>
                        <p><strong>Error Code:</strong> ${error.code || 'Unknown'}</p>
                        <p><strong>Error Message:</strong> ${error.message}</p>
                        <p><strong>Full Error:</strong> ${JSON.stringify(error, null, 2)}</p>
                    </div>
                `;
            }
        };

        async function testBackendAPI(idToken) {
            const resultDiv = document.getElementById('result');
            
            try {
                // Test Google Sign-In endpoint
                const response = await fetch('http://localhost:5000/api/auth/google', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ idToken })
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    resultDiv.innerHTML += `
                        <div class="result success">
                            <h3>✅ Backend API Test Successful!</h3>
                            <p><strong>User:</strong> ${data.user.firstName} ${data.user.lastName}</p>
                            <p><strong>Role:</strong> ${data.user.role}</p>
                            <p><strong>Token:</strong> ${data.token.substring(0, 50)}...</p>
                        </div>
                    `;
                } else {
                    // If user doesn't exist, that's expected for new users
                    if (data.message && data.message.includes('does not exist')) {
                        resultDiv.innerHTML += `
                            <div class="result">
                                <h3>ℹ️ User Not Found (Expected for New Users)</h3>
                                <p>This is normal for new Google users. They would be redirected to role selection.</p>
                                <p><strong>Message:</strong> ${data.message}</p>
                            </div>
                        `;
                    } else {
                        resultDiv.innerHTML += `
                            <div class="result error">
                                <h3>❌ Backend API Test Failed</h3>
                                <p><strong>Status:</strong> ${response.status}</p>
                                <p><strong>Message:</strong> ${data.message}</p>
                            </div>
                        `;
                    }
                }
                
            } catch (error) {
                resultDiv.innerHTML += `
                    <div class="result error">
                        <h3>❌ Backend API Connection Failed</h3>
                        <p><strong>Error:</strong> ${error.message}</p>
                        <p>Make sure the backend server is running on http://localhost:5000</p>
                    </div>
                `;
            }
        }

        window.clearResults = function() {
            document.getElementById('result').innerHTML = '';
        };
    </script>
</body>
</html>
