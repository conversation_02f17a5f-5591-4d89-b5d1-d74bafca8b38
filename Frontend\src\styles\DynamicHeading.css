/* DynamicHeading Component Styles */
.DynamicHeading {
  width: 100%;
  margin-bottom: var(--heading6);
}

.DynamicHeading__border {
  border-bottom: 1px solid #fddcdc;
  width: 100%;
}

.DynamicHeading__title {
  display: inline-flex;
  align-items: center;
  gap: var(--extrasmallfont);
  background-color: #fddcdc;
  color: #0a0033;
  padding: var(--extrasmallfont) var(--basefont);
  border-top-left-radius: var(--border-radius);
  border-top-right-radius: var(--heading5);
  border-bottom-left-radius: 0;
  position: relative;
  font-weight: 600;
  font-family: sans-serif;
  font-size: var(--heading5);
  margin: 0;
  clip-path: polygon(0 0, 95% 0, 100% 100%, 0% 100%);
  transition: all 0.3s ease;
}

.DynamicHeading__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--heading6);
  color: #0a0033;
}

.DynamicHeading__text {
  color: #0a0033;
  font-weight: 600;
  font-size: var(--heading5);
}

/* Hover effect for interactive feel */
.DynamicHeading__title:hover {
  background-color: #fcc8c8;
  transform: translateY(-1px);
}

/* Responsive styles */
@media (max-width: 1024px) {
  .DynamicHeading__title {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--heading6);
  }
  
  .DynamicHeading__text {
    font-size: var(--heading6);
  }
  
  .DynamicHeading__icon {
    font-size: var(--basefont);
  }
}

@media (max-width: 768px) {
  .DynamicHeading {
    margin-bottom: var(--basefont);
  }
  
  .DynamicHeading__title {
    padding: var(--extrasmallfont) var(--smallfont);
    font-size: var(--basefont);
    gap: var(--extrasmallfont);
  }
  
  .DynamicHeading__text {
    font-size: var(--basefont);
  }
  
  .DynamicHeading__icon {
    font-size: var(--smallfont);
  }
}

@media (max-width: 480px) {
  .DynamicHeading__title {
    padding: 6px var(--extrasmallfont);
    border-top-right-radius: var(--basefont);
  }
  
  .DynamicHeading__text {
    font-size: var(--smallfont);
  }
  
  .DynamicHeading__icon {
    font-size: var(--extrasmallfont);
  }
}
