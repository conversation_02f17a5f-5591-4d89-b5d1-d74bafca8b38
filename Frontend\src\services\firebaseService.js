import { initializeApp } from 'firebase/app';
import {
  getA<PERSON>,
  GoogleAuthProvider,
  signInWithPopup,
  signOut as firebaseSignOut
} from 'firebase/auth';
import { FIREBASE_CONFIG } from '../utils/constants';

// Initialize Firebase with error handling
let app;
let auth;
let googleProvider;

try {
  // Check if Firebase config is valid
  if (!FIREBASE_CONFIG.apiKey) {
    console.warn('Firebase configuration not found. Google authentication will not work until properly configured.');
    console.warn('Please follow the setup guide in QUICK_FIREBASE_SETUP.md');
  } else {
    app = initializeApp(FIREBASE_CONFIG);
    auth = getAuth(app);
    googleProvider = new GoogleAuthProvider();

    // Configure Google provider
    googleProvider.addScope('email');
    googleProvider.addScope('profile');

    console.log('Firebase initialized successfully');
  }
} catch (error) {
  console.error('Firebase initialization error:', error);
  console.warn('Please check your Firebase configuration and follow QUICK_FIREBASE_SETUP.md');
}

class FirebaseService {
  // Check if Firebase is properly initialized
  isInitialized() {
    return auth && googleProvider;
  }

  // Check if Firebase is configured with real credentials
  isProperlyConfigured() {
    return auth && googleProvider && FIREBASE_CONFIG.apiKey && FIREBASE_CONFIG.apiKey.length > 20;
  }

  // Sign in with Google
  async signInWithGoogle() {
    try {
      if (!this.isInitialized()) {
        throw new Error('Firebase is not initialized. Please check your configuration.');
      }

      if (!this.isProperlyConfigured()) {
        throw new Error('Firebase is not properly configured. Please set up your Firebase project and update the environment variables.');
      }

      // Configure Google provider with additional settings to handle COOP issues
      googleProvider.setCustomParameters({
        prompt: 'select_account'
      });

      const result = await signInWithPopup(auth, googleProvider);
      const user = result.user;

      // Get the ID token
      const idToken = await user.getIdToken();

      return {
        user: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          photoURL: user.photoURL,
          emailVerified: user.emailVerified,
        },
        idToken,
      };
    } catch (error) {
      console.error('Google sign-in error:', error);
      throw this.handleFirebaseError(error);
    }
  }

  // Sign out
  async signOut() {
    try {
      await firebaseSignOut(auth);
    } catch (error) {
      console.error('Sign out error:', error);
      throw this.handleFirebaseError(error);
    }
  }

  // Get current user
  getCurrentUser() {
    return auth.currentUser;
  }

  // Get ID token for current user
  async getCurrentUserToken() {
    const user = auth.currentUser;
    if (user) {
      return await user.getIdToken();
    }
    return null;
  }

  // Handle Firebase errors
  handleFirebaseError(error) {
    console.error('Firebase error details:', error);

    // Handle Cross-Origin-Opener-Policy errors
    if (error.message && error.message.includes('Cross-Origin-Opener-Policy')) {
      return 'Browser security settings are blocking the sign-in popup. Please try refreshing the page or using a different browser.';
    }

    if (error.code) {
      switch (error.code) {
        case 'auth/popup-closed-by-user':
          return 'Sign-in was cancelled. Please try again.';
        case 'auth/popup-blocked':
          return 'Pop-up was blocked by your browser. Please allow pop-ups and try again.';
        case 'auth/cancelled-popup-request':
          return 'Sign-in was cancelled. Please try again.';
        case 'auth/network-request-failed':
          return 'Network error. Please check your connection and try again.';
        case 'auth/too-many-requests':
          return 'Too many failed attempts. Please try again later.';
        case 'auth/user-disabled':
          return 'This account has been disabled. Please contact support.';
        case 'auth/operation-not-allowed':
          return 'Google sign-in is not enabled. Please contact support.';
        case 'auth/unauthorized-domain':
          return 'This domain is not authorized for Google sign-in. Please contact support.';
        case 'auth/web-storage-unsupported':
          return 'Your browser does not support web storage. Please try a different browser.';
        default:
          return error.message || 'An error occurred during authentication.';
      }
    }

    return error.message || 'An unexpected error occurred. Please try again.';
  }

  // Listen to auth state changes
  onAuthStateChanged(callback) {
    return auth.onAuthStateChanged(callback);
  }
}

export default new FirebaseService();
