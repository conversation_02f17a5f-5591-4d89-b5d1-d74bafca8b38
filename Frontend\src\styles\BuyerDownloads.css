.BuyerDownloads {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.BuyerDownloads__list {
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--basefont);
}

.BuyerDownloads__item {
  display: grid;
  grid-template-columns: auto 1fr auto;
  align-items: center;
  padding: var(--basefont);
  border: 1px solid var(--light-gray);
  border-radius: var(--border-radius);
  background-color: var(--white);
  transition: box-shadow 0.3s ease;
  gap: 10px;
}

.BuyerDownloads__item:hover {
  box-shadow: var(--box-shadow-light);
}

.BuyerDownloads__item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius);
  background-color: var(--bg-gray);
}

.BuyerDownloads__file-icon {
  font-size: 24px;
  color: var(--dark-gray);
}

.BuyerDownloads__file-icon--pdf {
  color: #ff3b30;
}

.BuyerDownloads__file-icon--video {
  color: #007aff;
}

.BuyerDownloads__item-info {
  flex: 1;
}

.BuyerDownloads__item-title {
  font-size: var(--heading6);
  color: var(--text-color);
  margin-bottom: 4px;
  font-weight: 500;
}

.BuyerDownloads__item-coach {
  font-size: var(--smallfont);
  color: var(--dark-gray);
  margin-bottom: 8px;
}

.BuyerDownloads__item-details {
  display: flex;
  gap: 16px;
  font-size: var(--smallfont);
  color: var(--dark-gray);
}

.BuyerDownloads__download-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: var(--btn-color);
  color: var(--white);
  border: none;
  padding: 8px 16px;
  border-radius: var(--border-radius);
  cursor: pointer;
  transition: all 0.3s ease;
  font-weight: 500;
  font-size: var(--smallfont);
}

.BuyerDownloads__download-btn:hover {
  background: linear-gradient(
    to bottom,
    var(--primary-color),
    var(--btn-color)
  );
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(238, 52, 37, 0.3);
}

.BuyerDownloads__empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  background-color: var(--bg-gray);
  border-radius: var(--border-radius);
  padding: 24px;
  color: var(--dark-gray);
  font-size: var(--basefont);
  text-align: center;
}

/* Responsive styles */
@media (max-width: 768px) {
  .BuyerDownloads__item {
    flex-direction: column;
    align-items: flex-start;
  }

  .BuyerDownloads__item-icon {
    margin-right: 0;
    margin-bottom: 16px;
  }

  .BuyerDownloads__item-info {
    width: 100%;
    margin-bottom: 16px;
  }

  .BuyerDownloads__item-details {
    flex-direction: column;
    gap: 4px;
  }

  .BuyerDownloads__download-btn {
    width: 100%;
    justify-content: center;
  }
}
